# Images Directory Structure

This directory contains all images used in the Fine Express Cargo website.

## Directory Structure

```
images/
├── logo.png                    # Company logo (40x40px recommended)
├── favicon.ico                 # Website favicon
├── company-intro.jpg           # Company introduction image
├── about-overview.jpg          # About page overview image
├── icons/                      # UI icons (SVG format recommended)
│   ├── import.svg
│   ├── export.svg
│   ├── comoro.svg
│   ├── transhipment.svg
│   ├── customs.svg
│   ├── loading.svg
│   ├── transit.svg
│   ├── mission.svg
│   ├── vision.svg
│   ├── values.svg
│   ├── experience.svg
│   ├── network.svg
│   ├── support.svg
│   ├── tracking.svg
│   ├── compliance.svg
│   ├── competitive.svg
│   ├── iso-cert.svg
│   ├── maritime-cert.svg
│   ├── safety-cert.svg
│   ├── environmental-cert.svg
│   ├── location.svg
│   ├── phone.svg
│   ├── email.svg
│   ├── clock.svg
│   ├── facebook.svg
│   ├── linkedin.svg
│   ├── whatsapp.svg
│   ├── x.svg
│   └── arrow-up.svg
├── flags/                      # Language flag images
│   ├── en.png                  # English flag (20x15px)
│   └── tz.png                  # Tanzania flag (20x15px)
├── vessels/                    # Partner vessel images
│   ├── lct-falk-njema.jpg
│   ├── lct-kota-moron.jpg
│   ├── mv-kaize.jpg
│   ├── mv-samba.jpg
│   ├── mv-safia-iv.jpg
│   ├── lct-zbar.jpg
│   ├── lct-wamarine.jpg
│   ├── mv-fadhil-rabbi.jpg
│   ├── mv-legena.jpg
│   └── mv-haiyuan.jpg
├── gallery/                    # Gallery images
│   ├── port-operations-1.jpg
│   ├── port-operations-2.jpg
│   ├── port-operations-3.jpg
│   ├── cargo-handling-1.jpg
│   ├── cargo-handling-2.jpg
│   ├── cargo-handling-3.jpg
│   ├── vessel-lct-falk-njema.jpg
│   ├── vessel-mv-kaize.jpg
│   ├── vessel-mv-samba.jpg
│   ├── warehouse-facility-1.jpg
│   ├── logistics-center.jpg
│   ├── office-facility.jpg
│   ├── team-operations.jpg
│   ├── team-supervision.jpg
│   ├── team-meeting.jpg
│   ├── documentation-1.jpg
│   ├── documentation-2.jpg
│   └── documentation-3.jpg
└── team/                       # Team member photos
    ├── maulidi-rashidi.jpg
    ├── halima-nassoro.jpg
    ├── mwalimu-khamis.jpg
    ├── salum-said.jpg
    ├── hamis-maulidi.jpg
    ├── hawa-uwesu.jpg
    └── ruwaida-afli.jpg
```

## Image Requirements

### General Guidelines
- **Format**: Use JPEG for photos, PNG for logos/graphics with transparency, SVG for icons
- **Quality**: High quality but optimized for web (compress images to reduce file size)
- **Naming**: Use descriptive, lowercase names with hyphens (kebab-case)

### Specific Requirements

#### Logo & Branding
- **logo.png**: 40x40px minimum, transparent background preferred
- **favicon.ico**: 16x16px, 32x32px, and 48x48px sizes included

#### Hero & Section Images
- **Dimensions**: Minimum 1200px width for hero images
- **Aspect Ratio**: 16:9 or 3:2 for landscape images
- **File Size**: Keep under 500KB per image

#### Team Photos
- **Dimensions**: 300x300px minimum (square format)
- **Style**: Professional headshots with consistent lighting
- **Background**: Neutral or company-branded background

#### Vessel Images
- **Dimensions**: 400x300px minimum
- **Quality**: High-resolution showing vessels clearly
- **Context**: Images showing vessels in operation preferred

#### Gallery Images
- **Dimensions**: 600x400px minimum
- **Categories**: Organize by port operations, cargo handling, vessels, facilities, team, documentation
- **Variety**: Show different aspects of operations

#### Icons
- **Format**: SVG preferred for scalability
- **Style**: Consistent line weight and style
- **Size**: 24x24px base size, scalable
- **Color**: Single color (will be styled via CSS)

#### Flags
- **Dimensions**: 20x15px (4:3 aspect ratio)
- **Quality**: Clear, recognizable flag designs
- **Format**: PNG with transparency if needed

## Optimization Tips

1. **Compress Images**: Use tools like TinyPNG or ImageOptim
2. **Responsive Images**: Consider providing multiple sizes for different screen densities
3. **Lazy Loading**: Images are set up for lazy loading in the code
4. **Alt Text**: Ensure all images have descriptive alt text in the HTML
5. **WebP Format**: Consider using WebP format for better compression (with fallbacks)

## Placeholder Images

If you don't have specific images yet, you can use placeholder services:
- **Unsplash**: https://unsplash.com/ (free high-quality photos)
- **Pexels**: https://pexels.com/ (free stock photos)
- **Placeholder.com**: https://placeholder.com/ (simple placeholder images)

## Image Sources

When adding images, ensure you have proper rights to use them:
- Company-owned photos
- Stock photos with proper licensing
- Creative Commons images with attribution
- Custom photography commissioned for the website

## Performance Considerations

- Keep total image payload under 2MB for the entire site
- Use appropriate compression levels
- Consider using a CDN for image delivery
- Implement responsive images for different screen sizes
- Use modern image formats (WebP, AVIF) with fallbacks

---

**Note**: Replace this README with actual images when implementing the website.
