/* ===== FINE EXPRESS CARGO - MAIN STYLESHEET ===== */

/* ===== CSS VARIABLES ===== */
:root {
    /* Fine Express Cargo Brand Colors */
    --primary-color: #1e3a8a;
    --primary-dark: #1e40af;
    --secondary-color: #fb923c;
    --accent-color: #fb923c;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Light Theme Background & Text */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* Border & Shadow */
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;

    /* Dark theme specific colors for better contrast */
    --primary-color: #3b82f6;
    --secondary-color: #fb923c;
    --accent-color: #fb923c;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* ===== ANIMATED MARITIME BACKGROUND ===== */
.maritime-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -10;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
    overflow: hidden;
    opacity: 0.95;
}

[data-theme="dark"] .maritime-background {
    opacity: 1;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

/* ===== VANTA.WAVES BACKGROUND ===== */
.vanta-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

/* Fallback background for when VANTA is loading */
.vanta-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: -1;
}

/* Animated Wave Elements */
.wave {
    position: absolute;
    width: 200%;
    height: 200px;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.1) 100%);
    border-radius: 50px;
    animation: wave-flow 15s ease-in-out infinite;
    transform-origin: center;
}

.wave:nth-child(1) {
    top: 20%;
    left: -50%;
    animation-delay: 0s;
}

.wave:nth-child(2) {
    top: 60%;
    left: -50%;
    animation-delay: -5s;
}

.wave:nth-child(3) {
    top: 40%;
    left: -50%;
    animation-delay: -10s;
}

[data-theme="dark"] .wave {
    background: linear-gradient(45deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 50%, rgba(255,255,255,0.05) 100%);
}

/* Floating Ship Icons */
.ship-icon {
    position: absolute;
    width: 60px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 0 20px 20px 0;
    animation: float-ship 20s linear infinite;
}

.ship-icon::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 15px;
    width: 30px;
    height: 20px;
    background: rgba(255,255,255,0.08);
    border-radius: 5px;
}

.ship-icon:nth-child(4) {
    top: 25%;
    left: -100px;
    animation-delay: 0s;
}

.ship-icon:nth-child(5) {
    top: 70%;
    left: -100px;
    animation-delay: -10s;
}

[data-theme="dark"] .ship-icon {
    background: rgba(255,255,255,0.05);
}

[data-theme="dark"] .ship-icon::before {
    background: rgba(255,255,255,0.03);
}

/* Container Icons */
.container-icon {
    position: absolute;
    width: 40px;
    height: 30px;
    background: rgba(255,255,255,0.08);
    border-radius: 3px;
    animation: float-container 18s ease-in-out infinite;
}

.container-icon:nth-child(6) {
    top: 15%;
    right: -50px;
    animation-delay: -3s;
}

.container-icon:nth-child(7) {
    top: 45%;
    right: -50px;
    animation-delay: -8s;
}

.container-icon:nth-child(8) {
    top: 75%;
    right: -50px;
    animation-delay: -13s;
}

[data-theme="dark"] .container-icon {
    background: rgba(255,255,255,0.04);
}

/* Scroll-triggered Animation */
.scroll-element {
    transition: transform 0.3s ease-out;
}

/* Maritime Background Keyframe Animations */
@keyframes wave-flow {
    0%, 100% { transform: translateX(0) rotate(0deg); }
    50% { transform: translateX(50px) rotate(2deg); }
}

@keyframes float-ship {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(25vw) translateY(-10px); }
    50% { transform: translateX(50vw) translateY(5px); }
    75% { transform: translateX(75vw) translateY(-5px); }
    100% { transform: translateX(100vw) translateY(0); }
}

@keyframes float-container {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    25% { transform: translateX(-25vw) translateY(-15px) rotate(-2deg); }
    50% { transform: translateX(-50vw) translateY(10px) rotate(1deg); }
    75% { transform: translateX(-75vw) translateY(-8px) rotate(-1deg); }
    100% { transform: translateX(-100vw) translateY(0) rotate(0deg); }
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

ul, ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-xl);
}

li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

/* ===== LAYOUT UTILITIES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}

.section {
    padding: var(--spacing-3xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
    min-height: 44px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== HEADER ===== */
.header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    z-index: var(--z-sticky);
}

.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .sticky-header {
    background-color: rgba(15, 23, 42, 0.95);
}

/* ===== MODAL STYLES ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    padding: var(--spacing-md);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--text-muted);
    transition: color var(--transition-fast);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
}

/* ===== FORM STYLES ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 768px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: inherit;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    min-height: 44px;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input.error,
select.error,
textarea.error {
    border-color: var(--error-color);
}

.error-message {
    display: block;
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.error-message.show {
    opacity: 1;
}

textarea {
    resize: vertical;
    min-height: 100px;
}

select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Quote Form */
.quote-form {
    padding: var(--spacing-xl);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    overflow: hidden;
}

/* Hero Slideshow */
.hero-slideshow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-slide[data-bg] {
    background-image: var(--slide-bg);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    z-index: -1;
}

[data-theme="dark"] .hero-overlay {
    background: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .indicator {
    border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .indicator:hover {
    border-color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .indicator.active {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.9);
}

/* Slideshow Indicators */
.slideshow-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 2;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.indicator:hover {
    border-color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.3);
}

.indicator.active {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 1);
}

/* Responsive hero slideshow */
@media (max-width: 768px) {
    .hero-slide {
        background-position: center bottom;
    }

    .slideshow-indicators {
        bottom: 20px;
        gap: 8px;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    padding: var(--spacing-xl);
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--spacing-lg);
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.9),
        0 4px 8px rgba(0, 0, 0, 0.7),
        0 6px 12px rgba(0, 0, 0, 0.5),
        0 1px 0px rgba(0, 0, 0, 1);
    background: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(5px);
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    color: #ffffff !important;
    opacity: 0.95;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
    background: rgba(0, 0, 0, 0.25);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    backdrop-filter: blur(3px);
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

@media (min-width: 640px) {
    .hero-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* ===== COMPANY INTRO SECTION ===== */
.company-intro {
    padding: var(--spacing-3xl) 0;
    background-color: var(--bg-secondary);
}

.intro-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

@media (min-width: 768px) {
    .intro-content {
        grid-template-columns: 1fr 1fr;
    }
}

.intro-text h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.intro-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

.intro-image {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.intro-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.intro-image:hover img {
    transform: scale(1.05);
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--spacing-3xl) 0;
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.service-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    border-radius: var(--radius-xl);
    transition: transform var(--transition-normal);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-icon img {
    width: 30px;
    height: 30px;
    filter: brightness(0) invert(1);
}

[data-theme="dark"] .service-icon img {
    filter: brightness(0) invert(0.8);
}

.service-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== PARTNERS SECTION ===== */
.partners {
    padding: var(--spacing-3xl) 0;
    background-color: var(--bg-secondary);
}

.partners-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .partners-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.partner-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.partner-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.partner-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.partner-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.partner-card:hover .partner-image img {
    transform: scale(1.1);
}

.partner-info {
    padding: var(--spacing-lg);
    text-align: center;
}

.partner-info h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.partner-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== CTA SECTION ===== */
.cta {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-lg);
    color: white;
}

.cta p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2xl);
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

@media (min-width: 640px) {
    .cta-actions {
        flex-direction: row;
        justify-content: center;
    }
}

.cta .btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta .btn-secondary:hover {
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--spacing-3xl) 0;
    background-color: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
}

@media (min-width: 1024px) {
    .contact-content {
        grid-template-columns: 1fr 1fr;
    }
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

.contact-details h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-lg);
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-details a:hover {
    color: var(--primary-dark);
}

.phone-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 500;
    margin-left: var(--spacing-xs);
}

.contact-form-container {
    background-color: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.contact-form {
    margin: 0;
}

/* ===== PAGE HEADER ===== */
.page-header {
    padding: calc(80px + var(--spacing-3xl)) 0 var(--spacing-3xl);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="page-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23page-pattern)"/></svg>');
    opacity: 0.3;
}

.page-header-content {
    position: relative;
    z-index: 1;
}

.page-header h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    color: white;
}

.page-header p {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== ABOUT PAGE STYLES ===== */
.company-overview {
    padding: var(--spacing-3xl) 0;
}

.overview-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

@media (min-width: 1024px) {
    .overview-content {
        grid-template-columns: 2fr 1fr;
    }
}

.overview-text h2 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.overview-image {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.overview-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.overview-image:hover img {
    transform: scale(1.05);
}

/* Mission, Vision, Values */
.mission-vision-values {
    padding: var(--spacing-3xl) 0;
    background-color: var(--bg-secondary);
}

.mvv-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 1024px) {
    .mvv-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.mvv-card {
    background-color: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.mvv-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.mvv-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background-color: var(--primary-color);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mvv-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.mvv-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.mvv-card p,
.mvv-card ul {
    color: var(--text-secondary);
    text-align: left;
}

.mvv-card ul {
    list-style: none;
    padding: 0;
}

.mvv-card li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
}

.mvv-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Why Choose Us */
.why-choose-us {
    padding: var(--spacing-3xl) 0;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

.feature-item h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.feature-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== TEAM SECTION ===== */
.our-team {
    padding: var(--spacing-3xl) 0;
    background-color: var(--bg-secondary);
}

.team-structure {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
}

.team-category {
    background-color: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

.team-category-title {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
}

.team-category-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.team-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.team-member {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.member-photo {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
    box-shadow: var(--shadow-md);
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.team-member:hover .member-photo img {
    transform: scale(1.1);
}

.member-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-lg);
}

.member-position {
    color: var(--primary-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.member-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== CERTIFICATIONS SECTION ===== */
.certifications {
    padding: var(--spacing-3xl) 0;
}

.certifications-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .certifications-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .certifications-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.certification-item {
    background-color: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.certification-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.cert-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
    background-color: var(--primary-color);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
}

.cert-icon img {
    width: 30px;
    height: 30px;
    filter: brightness(0) invert(1);
}

.certification-item h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.certification-item p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== GALLERY STYLES ===== */
.gallery-filters {
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-secondary);
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.gallery-grid-section {
    padding: var(--spacing-3xl) 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 640px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .gallery-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.gallery-item.hidden {
    display: none;
}

.gallery-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.2) 50%, transparent 100%);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: var(--spacing-lg);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    color: white;
    margin-bottom: var(--spacing-md);
}

.gallery-info h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: white;
}

.gallery-info p {
    font-size: var(--font-size-sm);
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
}

.gallery-view-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    align-self: flex-start;
}

.gallery-view-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.gallery-load-more {
    text-align: center;
    margin-top: var(--spacing-2xl);
}

/* ===== LIGHTBOX MODAL ===== */
.lightbox-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    padding: var(--spacing-md);
}

.lightbox-modal.show {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-3xl);
    cursor: pointer;
    z-index: 1;
    transition: color var(--transition-fast);
}

.lightbox-close:hover {
    color: var(--primary-color);
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1;
}

.lightbox-prev {
    left: -70px;
}

.lightbox-next {
    right: -70px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background-color: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-image-container {
    position: relative;
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.lightbox-image-container img {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

.lightbox-info {
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
}

.lightbox-info h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.lightbox-info p {
    color: var(--text-secondary);
    margin: 0;
}

.lightbox-counter {
    text-align: center;
    color: white;
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

@media (min-width: 640px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

.footer-section h3 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.footer-logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.footer-logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
    font-size: var(--font-size-sm);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-contact p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.footer-contact strong {
    color: var(--text-primary);
}

.footer-contact a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-contact a:hover {
    color: var(--primary-dark);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-xl);
}

.footer-bottom-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    text-align: center;
}

@media (min-width: 768px) {
    .footer-bottom-content {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--border-color);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

.social-link img {
    width: 20px;
    height: 20px;
    filter: brightness(0) saturate(100%) invert(45%) sepia(7%) saturate(1190%) hue-rotate(202deg) brightness(94%) contrast(86%);
    transition: filter var(--transition-fast);
}

.social-link:hover img {
    filter: brightness(0) invert(1);
}

/* WhatsApp specific styling */
.social-link[aria-label="WhatsApp"]:hover {
    background-color: #25D366;
    opacity: 0.9;
}

[data-theme="dark"] .social-link[aria-label="WhatsApp"]:hover {
    background-color: #128C7E;
}

.social-link[aria-label="WhatsApp"] {
    transition: all var(--transition-fast);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background-color: var(--primary-dark);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.back-to-top img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
}

/* ===== MESSAGE CONTAINER ===== */
.message-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-xl);
    z-index: var(--z-modal);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
}

.message {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transform: translateX(100%);
    transition: all var(--transition-normal);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.message.show {
    transform: translateX(0);
}

.message.success {
    background-color: var(--success-color);
    color: white;
}

.message.error {
    background-color: var(--error-color);
    color: white;
}

.message.warning {
    background-color: var(--warning-color);
    color: white;
}

.message-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: var(--font-size-lg);
    margin-left: auto;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
}

.message-close:hover {
    opacity: 1;
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 639px) {
    .hidden-mobile { display: none !important; }
    .visible-mobile { display: block !important; }
}

@media (min-width: 640px) and (max-width: 1023px) {
    .hidden-tablet { display: none !important; }
    .visible-tablet { display: block !important; }
}

@media (min-width: 1024px) {
    .hidden-desktop { display: none !important; }
    .visible-desktop { display: block !important; }
}

/* ===== PRINT STYLES ===== */
@media print {
    .no-print {
        display: none !important;
    }

    .header,
    .footer,
    .back-to-top,
    .message-container {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: var(--text-primary);
        background: var(--bg-primary);
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .section {
        padding: 20pt 0;
    }

    h1, h2, h3, h4, h5, h6 {
        color: var(--text-primary);
        page-break-after: avoid;
    }

    p, li {
        orphans: 3;
        widows: 3;
    }

    .page-break {
        page-break-before: always;
    }
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    min-height: 80px;
}

/* Logo */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.logo-text {
    color: var(--primary-color);
}

/* Navigation */
.nav-menu {
    display: none;
}

@media (min-width: 1024px) {
    .nav-menu {
        display: block;
    }
}

@media (max-width: 1023px) {
    .nav-menu {
        display: block;
    }
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary);
    padding: var(--spacing-sm) 0;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 1px;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Admin Login Button */
.admin-login-section {
    display: flex;
    align-items: center;
}

.admin-login-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid transparent;
}

.admin-login-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.admin-login-btn svg {
    width: 20px;
    height: 20px;
    transition: transform var(--transition-fast);
}

.admin-login-btn:hover svg {
    transform: scale(1.1);
}

.admin-login-text {
    font-weight: 500;
}

@media (max-width: 768px) {
    .admin-login-text {
        display: none;
    }

    .admin-login-btn {
        padding: 0.5rem;
    }
}

/* Quote Button */
.quote-btn {
    display: none;
}

@media (min-width: 768px) {
    .quote-btn {
        display: inline-flex;
    }
}

/* Language Switcher */
.language-switcher {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.lang-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.flag-icon {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: 2px;
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: var(--z-dropdown);
    margin-top: var(--spacing-xs);
}

.lang-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* ===== TEXT VISIBILITY FIXES FOR MARITIME BACKGROUND ===== */
/* Homepage Services Section */
.services .section-title,
.services .section-subtitle {
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
}

/* About Page - Our Story Section */
.company-overview h2,
.company-overview .overview-text p {
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
}

/* About Page - Why Choose Us Section */
.why-choose-us .section-title,
.why-choose-us .section-subtitle {
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
}

/* About Page - Certifications Section */
.certifications .section-title,
.certifications .section-subtitle {
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
}

/* Gallery Page Load More Button */
#load-more-btn {
    color: #ffffff !important;
    text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Gallery Filter Buttons - Enhanced visibility */
.filter-btn {
    text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.6);
}

.filter-btn.active {
    color: #ffffff !important;
    text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Gallery Section Headings (if any) */
.gallery-header .section-title,
.gallery-header .section-subtitle {
    color: #ffffff !important;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.8),
        0 4px 8px rgba(0, 0, 0, 0.6),
        0 1px 0px rgba(0, 0, 0, 0.9);
}

/* Theme Compatibility - Ensure white text in both themes */
[data-theme="light"] .hero-title,
[data-theme="light"] .hero-subtitle,
[data-theme="light"] .services .section-title,
[data-theme="light"] .services .section-subtitle,
[data-theme="light"] .company-overview h2,
[data-theme="light"] .company-overview .overview-text p,
[data-theme="light"] .why-choose-us .section-title,
[data-theme="light"] .why-choose-us .section-subtitle,
[data-theme="light"] .certifications .section-title,
[data-theme="light"] .certifications .section-subtitle {
    color: #ffffff !important;
}

[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .services .section-title,
[data-theme="dark"] .services .section-subtitle,
[data-theme="dark"] .company-overview h2,
[data-theme="dark"] .company-overview .overview-text p,
[data-theme="dark"] .why-choose-us .section-title,
[data-theme="dark"] .why-choose-us .section-subtitle,
[data-theme="dark"] .certifications .section-title,
[data-theme="dark"] .certifications .section-subtitle {
    color: #ffffff !important;
}

/* ===== MARITIME BACKGROUND RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .ship-icon, .container-icon {
        transform: scale(0.7);
    }

    .wave {
        height: 150px;
    }

    .maritime-background {
        opacity: 0.9;
    }
}

@media (max-width: 480px) {
    .ship-icon, .container-icon {
        transform: scale(0.5);
    }

    .wave {
        height: 120px;
    }

    .maritime-background {
        opacity: 0.85;
    }
}

.lang-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.lang-option:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.lang-option:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.lang-option:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Theme Toggle */
.theme-toggle {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: var(--border-color);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: background-color var(--transition-normal);
    overflow: hidden;
}

.theme-toggle:hover {
    background-color: var(--primary-color);
}

.theme-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    transition: all var(--transition-normal);
}

.sun-icon {
    left: 4px;
    opacity: 1;
}

.moon-icon {
    right: 4px;
    opacity: 0;
}

[data-theme="dark"] .sun-icon {
    opacity: 0;
}

[data-theme="dark"] .moon-icon {
    opacity: 1;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

@media (min-width: 1024px) {
    .mobile-menu-toggle {
        display: none;
    }
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background-color: var(--text-primary);
    margin: 2px 0;
    transition: all var(--transition-fast);
    border-radius: 1px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
@media (max-width: 1023px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background-color: var(--bg-primary);
        border-bottom: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: var(--z-dropdown);
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.show {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-list {
        flex-direction: column;
        padding: var(--spacing-lg) 0;
        gap: 0;
        margin: 0;
        list-style: none;
    }

    .nav-link {
        display: block;
        padding: var(--spacing-md) var(--spacing-xl);
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 500;
        transition: all var(--transition-fast);
    }

    .nav-link:hover {
        background-color: var(--bg-secondary);
        color: var(--primary-color);
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }
}

/* ===== SCHEDULE SECTION STYLES ===== */
.schedule {
    padding: var(--spacing-section) 0;
    background-color: var(--bg-secondary);
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.schedule-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.schedule-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.schedule-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schedule-icon img {
    width: 30px;
    height: 30px;
    filter: brightness(0) invert(1);
}

.schedule-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.schedule-days {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.schedule-location {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ===== SIGNUP SECTION STYLES ===== */
.signup-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.signup-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.signup-info h3 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.signup-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

.signup-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.signup-benefits li {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: var(--font-weight-medium);
    padding-left: 0.5rem;
    font-size: 1rem;
}

.signup-form-container {
    background: var(--bg-secondary);
    padding: 2.5rem;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.signup-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.signup-form .form-group {
    margin-bottom: var(--spacing-md);
}

.signup-form input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.signup-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* ===== OFFICE SECTION STYLES ===== */
.office-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.office-section:last-of-type {
    border-bottom: none;
    margin-bottom: var(--spacing-lg);
}

.office-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-bold);
}

.contact-item h4 {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

/* ===== RESPONSIVE DESIGN FOR NEW SECTIONS ===== */
@media (max-width: 768px) {
    .schedule-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .signup-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .signup-form .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .office-section {
        margin-bottom: var(--spacing-lg);
    }
}

/* ===== WHATSAPP INTEGRATION STYLES ===== */
.whatsapp-btn {
    background-color: #25D366;
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.whatsapp-btn:hover {
    background-color: #128C7E;
}

.whatsapp-btn::before {
    content: "📱";
    font-size: var(--font-size-sm);
}

/* ===== ARABIC LANGUAGE SUPPORT ===== */
[lang="ar"] {
    direction: rtl;
    text-align: right;
}

[lang="ar"] .container {
    direction: rtl;
}

[lang="ar"] .nav-list {
    flex-direction: row-reverse;
}

[lang="ar"] .form-row {
    direction: rtl;
}

[lang="ar"] .signup-content {
    direction: rtl;
}

/* ===== FINE EXPRESS CARGO BACKGROUND ANIMATIONS ===== */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="cargo-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><rect width="100" height="100" fill="none"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23cargo-pattern)"/></svg>') repeat;
    animation: movePattern 20s linear infinite;
    opacity: 0.3;
}

@keyframes movePattern {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(100px) translateY(100px); }
}

.hero::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(251, 146, 60, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

/* ===== SECTION BACKGROUND ANIMATIONS ===== */
.services {
    background: linear-gradient(45deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(30, 58, 138, 0.1)"/></svg>') repeat;
    animation: floatDots 15s linear infinite;
}

@keyframes floatDots {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

.schedule {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--primary-color) 100%);
    position: relative;
}

.schedule::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><path d="M50,50 L150,50 M50,100 L150,100 M50,150 L150,150" stroke="rgba(251, 146, 60, 0.1)" stroke-width="2" fill="none"/></svg>') repeat;
    animation: slideLines 12s linear infinite;
}

@keyframes slideLines {
    0% { transform: translateX(0); }
    100% { transform: translateX(200px); }
}

/* ===== INTERACTIVE ELEMENT ANIMATIONS ===== */
.btn {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(251, 146, 60, 0.3);
}

/* ===== SERVICE CARD ANIMATIONS ===== */
.service-card {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(30, 58, 138, 0.15);
}

.service-card:hover .service-icon img {
    filter: brightness(0) saturate(100%) invert(45%) sepia(100%) saturate(1500%) hue-rotate(200deg);
}

/* ===== LOADING ANIMATIONS ===== */
.loading-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(30, 58, 138, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== CARGO ROUTE ANIMATION ===== */
.route-animation {
    position: relative;
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin: 2rem 0;
}

.route-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    animation: routeProgress 3s ease-in-out infinite;
}

@keyframes routeProgress {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: -100%; }
}

/* ===== FLOATING ELEMENTS ===== */
.floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* ===== RESPONSIVE ANIMATIONS ===== */
@media (max-width: 768px) {
    .hero::before,
    .services::before,
    .schedule::before {
        animation-duration: 30s;
    }

    .service-card:hover {
        transform: translateY(-5px) scale(1.01);
    }
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
    .hero::before,
    .services::before,
    .schedule::before,
    .btn::before,
    .route-animation::before {
        animation: none;
    }

    .service-card:hover,
    .btn:hover {
        transform: none;
    }
}
