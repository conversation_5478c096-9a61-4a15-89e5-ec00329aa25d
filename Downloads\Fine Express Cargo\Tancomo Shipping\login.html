<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Fine Express Cargo Admin Login - Secure access to dashboard">
    <meta name="author" content="Fine Express Cargo">
    
    <title>Admin Login - Fine Express Cargo</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/styles.css">
    
    <style>
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-family);
        }
        
        .login-container {
            background: white;
            padding: 3rem;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-logo {
            margin-bottom: 2rem;
        }
        
        .login-logo img {
            width: 80px;
            height: 80px;
            margin-bottom: 1rem;
        }
        
        .login-title {
            color: var(--primary-color);
            font-size: 1.75rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .form-group input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-size: 1rem;
            transition: border-color var(--transition-fast);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
        }
        
        .form-group input.error {
            border-color: var(--error-color);
        }
        
        .error-message {
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }
        
        .login-btn {
            width: 100%;
            padding: 0.875rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color var(--transition-fast);
            margin-bottom: 1rem;
        }
        
        .login-btn:hover {
            background: var(--primary-dark);
        }
        
        .login-btn:disabled {
            background: var(--text-muted);
            cursor: not-allowed;
        }
        
        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .security-note {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin-top: 2rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .loading {
            display: none;
            margin-left: 0.5rem;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius-sm);
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: var(--success-color);
        }
        
        .notification.error {
            background: var(--error-color);
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <img src="assets/images/logo.png" alt="Fine Express Cargo">
            <h1 class="login-title">Admin Login</h1>
            <p class="login-subtitle">Fine Express Cargo Dashboard</p>
        </div>
        
        <form id="login-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
                <div class="error-message" id="username-error">Please enter a valid username</div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
                <div class="error-message" id="password-error">Please enter your password</div>
            </div>
            
            <button type="submit" class="login-btn" id="login-btn">
                Sign In
                <span class="loading" id="loading">
                    <span class="spinner"></span>
                </span>
            </button>
            
            <a href="#" class="forgot-password" id="forgot-password">Forgot your password?</a>
        </form>
        
        <div class="security-note">
            <strong>Security Notice:</strong> This is a secure admin area. All login attempts are monitored and logged.
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const loading = document.getElementById('loading');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            
            // Demo credentials
            const validCredentials = {
                'admin': 'admin123',
                'fineexpress': 'cargo2024',
                'manager': 'manager123'
            };
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = usernameInput.value.trim();
                const password = passwordInput.value;
                
                // Clear previous errors
                clearErrors();
                
                // Validate inputs
                if (!username) {
                    showError('username', 'Username is required');
                    return;
                }
                
                if (!password) {
                    showError('password', 'Password is required');
                    return;
                }
                
                // Show loading state
                loginBtn.disabled = true;
                loading.classList.add('show');
                
                // Simulate authentication delay
                setTimeout(() => {
                    if (validCredentials[username] && validCredentials[username] === password) {
                        // Successful login
                        showNotification('Login successful! Redirecting...', 'success');
                        
                        // Store login state
                        localStorage.setItem('adminLoggedIn', 'true');
                        localStorage.setItem('adminUsername', username);
                        localStorage.setItem('loginTime', new Date().toISOString());
                        
                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = 'admin.html';
                        }, 1500);
                    } else {
                        // Failed login
                        showNotification('Invalid username or password', 'error');
                        showError('password', 'Invalid credentials');
                        
                        // Reset form
                        loginBtn.disabled = false;
                        loading.classList.remove('show');
                        passwordInput.value = '';
                        passwordInput.focus();
                    }
                }, 1500);
            });
            
            // Forgot password handler
            document.getElementById('forgot-password').addEventListener('click', function(e) {
                e.preventDefault();
                alert('Please contact the system administrator to reset your password.\n\nEmail: <EMAIL>\nPhone: +255 657 769 101');
            });
            
            // Clear errors on input
            usernameInput.addEventListener('input', () => clearError('username'));
            passwordInput.addEventListener('input', () => clearError('password'));
            
            // Check if already logged in
            if (localStorage.getItem('adminLoggedIn') === 'true') {
                const loginTime = new Date(localStorage.getItem('loginTime'));
                const now = new Date();
                const hoursSinceLogin = (now - loginTime) / (1000 * 60 * 60);
                
                if (hoursSinceLogin < 24) { // Session valid for 24 hours
                    window.location.href = 'admin.html';
                } else {
                    // Session expired
                    localStorage.removeItem('adminLoggedIn');
                    localStorage.removeItem('adminUsername');
                    localStorage.removeItem('loginTime');
                }
            }
        });
        
        function showError(field, message) {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + '-error');
            
            input.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function clearError(field) {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + '-error');
            
            input.classList.remove('error');
            errorDiv.style.display = 'none';
        }
        
        function clearErrors() {
            clearError('username');
            clearError('password');
        }
        
        function showNotification(message, type) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html>
